import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { Layout, Button, Typography, Card, Spin } from 'antd';
import { PlayCircleOutlined, SettingOutlined, BookOutlined } from '@ant-design/icons';
import './App.css';

// Simple fallback components
const SimpleAppBuilder = () => {
  const [components, setComponents] = useState([]);
  const [selectedComponent, setSelectedComponent] = useState(null);

  const addComponent = (type) => {
    const newComponent = {
      id: `component_${Date.now()}`,
      type,
      props: { text: `New ${type}` },
      position: { x: Math.random() * 300, y: Math.random() * 200 }
    };
    setComponents(prev => [...prev, newComponent]);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Layout.Header style={{ background: '#fff', padding: '0 24px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography.Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            App Builder 201
          </Typography.Title>
          <div style={{ display: 'flex', gap: '12px' }}>
            <Button type="primary" icon={<PlayCircleOutlined />}>
              Preview
            </Button>
            <Button icon={<SettingOutlined />}>
              Settings
            </Button>
            <Button icon={<BookOutlined />}>
              Help
            </Button>
          </div>
        </div>
      </Layout.Header>

      <Layout>
        <Layout.Sider width={250} style={{ background: '#fff', borderRight: '1px solid #f0f0f0' }}>
          <div style={{ padding: '16px' }}>
            <Typography.Title level={5}>Components</Typography.Title>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <Button onClick={() => addComponent('button')} block>
                Add Button
              </Button>
              <Button onClick={() => addComponent('text')} block>
                Add Text
              </Button>
              <Button onClick={() => addComponent('input')} block>
                Add Input
              </Button>
              <Button onClick={() => addComponent('card')} block>
                Add Card
              </Button>
            </div>
          </div>
        </Layout.Sider>

        <Layout.Content style={{ padding: '24px', background: '#f5f5f5' }}>
          <Card
            title="Canvas"
            style={{ height: '100%', minHeight: '500px' }}
            bodyStyle={{ position: 'relative', height: '100%' }}
          >
            {components.length === 0 ? (
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '300px',
                color: '#999'
              }}>
                <Typography.Title level={4} type="secondary">
                  Welcome to App Builder 201
                </Typography.Title>
                <Typography.Text type="secondary">
                  Start building by adding components from the left panel
                </Typography.Text>
              </div>
            ) : (
              components.map(component => (
                <div
                  key={component.id}
                  style={{
                    position: 'absolute',
                    left: component.position.x,
                    top: component.position.y,
                    padding: '8px',
                    border: selectedComponent?.id === component.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
                    borderRadius: '4px',
                    background: '#fff',
                    cursor: 'pointer'
                  }}
                  onClick={() => setSelectedComponent(component)}
                >
                  {component.type === 'button' && <Button>{component.props.text}</Button>}
                  {component.type === 'text' && <Typography.Text>{component.props.text}</Typography.Text>}
                  {component.type === 'input' && <input placeholder={component.props.text} />}
                  {component.type === 'card' && (
                    <Card size="small" style={{ width: 200 }}>
                      {component.props.text}
                    </Card>
                  )}
                </div>
              ))
            )}
          </Card>
        </Layout.Content>

        <Layout.Sider width={250} style={{ background: '#fff', borderLeft: '1px solid #f0f0f0' }}>
          <div style={{ padding: '16px' }}>
            <Typography.Title level={5}>Properties</Typography.Title>
            {selectedComponent ? (
              <div>
                <Typography.Text strong>Type:</Typography.Text>
                <Typography.Text> {selectedComponent.type}</Typography.Text>
                <br />
                <Typography.Text strong>ID:</Typography.Text>
                <Typography.Text> {selectedComponent.id}</Typography.Text>
              </div>
            ) : (
              <Typography.Text type="secondary">
                Select a component to edit its properties
              </Typography.Text>
            )}
          </div>
        </Layout.Sider>
      </Layout>
    </Layout>
  );
};

function App() {
  const [loading, setLoading] = useState(true);

  // Simple initialization
  useEffect(() => {
    console.log('🚀 Starting App Builder 201...');

    // Set React globals for debugging
    window.React = React;
    window.__REACT_VERSION__ = React.version;
    window.__REACT_LOADED__ = true;

    // Simple timeout to simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
      console.log('✅ App Builder 201 loaded successfully!');
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      }}>
        <Spin size="large" style={{ marginBottom: '20px' }} />
        <Typography.Title level={2} style={{ color: 'white', margin: 0 }}>
          Loading App Builder 201...
        </Typography.Title>
        <Typography.Text style={{ color: 'rgba(255,255,255,0.8)' }}>
          Preparing your development environment
        </Typography.Text>
      </div>
    );
  }


  return (
    <Router>
      <Helmet>
        <meta name="theme-color" content="#2563EB" />
        <meta name="description" content="App Builder 201 - Build your application with minimal setup" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
        <meta name="keywords" content="app builder, application development, low-code, no-code, development tool" />
        <meta name="author" content="App Builder Team" />
        <meta name="robots" content="index, follow" />
        <meta property="og:type" content="website" />
        <meta property="og:title" content="App Builder 201" />
        <meta property="og:description" content="Build your application with minimal setup" />
        <meta property="og:image" content="/logo512.png" />
        <meta property="og:url" content={window.location.href} />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="App Builder 201" />
        <meta name="twitter:description" content="Build your application with minimal setup" />
        <meta name="twitter:image" content="/logo512.png" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="apple-touch-icon" href="/logo192.png" />
      </Helmet>

      <div id="main-content">
        <SimpleAppBuilder />
      </div>

      {/* Development info */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'fixed',
          bottom: '20px',
          left: '20px',
          background: 'rgba(0,0,0,0.8)',
          color: 'white',
          padding: '12px',
          borderRadius: '8px',
          fontSize: '12px',
          zIndex: 1000
        }}>
          <div>React: {React.version}</div>
          <div>Environment: {process.env.NODE_ENV}</div>
          <div>Status: ✅ Running</div>
        </div>
      )}
    </Router>
  );
}

export default App;


