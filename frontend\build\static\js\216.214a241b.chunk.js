"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[216],{

/***/ 8833:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  A: () => (/* binding */ hooks_useAIDesignSuggestions)
});

// UNUSED EXPORTS: useAIDesignSuggestions

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(92901);
;// ./src/config/aiConfig.js


/**
 * AI Configuration
 * Centralized configuration for AI features and services
 */

// Environment variables
var API_URL = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_25412_JVTRZOZECLVMQHVG","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11708_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_API_URL || 'http://localhost:8000';
var AI_ENABLED = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_25412_JVTRZOZECLVMQHVG","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11708_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_ENABLED !== 'false'; // Default to true
var AI_FALLBACK_ENABLED = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_25412_JVTRZOZECLVMQHVG","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11708_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_FALLBACK_ENABLED !== 'false'; // Default to true

// AI Service Configuration
var aiConfig = {
  // Core settings
  enabled: AI_ENABLED,
  fallbackEnabled: AI_FALLBACK_ENABLED,
  // API settings
  baseUrl: "".concat(API_URL, "/api/ai"),
  timeout: 10000,
  // 10 seconds
  retryAttempts: 2,
  retryDelay: 1000,
  // 1 second

  // Cache settings
  cacheEnabled: true,
  cacheTimeout: 5 * 60 * 1000,
  // 5 minutes

  // WebSocket settings
  websocketEnabled: true,
  websocketTimeout: 10000,
  // 10 seconds

  // Feature flags
  features: {
    layoutSuggestions: true,
    componentCombinations: true,
    appAnalysis: true,
    realTimeUpdates: true,
    collaborativeAI: false // Experimental
  },
  // Fallback behavior
  fallback: {
    showWarnings: false,
    // Set to false to reduce console noise
    useBasicAnalysis: true,
    provideFallbackSuggestions: true,
    gracefulDegradation: true
  },
  // Performance settings
  performance: {
    debounceDelay: 500,
    // Debounce AI requests
    maxConcurrentRequests: 3,
    backgroundRefresh: true,
    lazyLoading: true
  }
};

// Helper functions
var isAIEnabled = function isAIEnabled() {
  return aiConfig.enabled;
};
var isFeatureEnabled = function isFeatureEnabled(feature) {
  return aiConfig.enabled && aiConfig.features[feature];
};
var shouldShowWarnings = function shouldShowWarnings() {
  return aiConfig.fallback.showWarnings;
};

// Service availability checker
var checkAIServiceAvailability = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee() {
    var controller, timeoutId, response, _t;
    return _regeneratorRuntime.wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (aiConfig.enabled) {
            _context.next = 1;
            break;
          }
          return _context.abrupt("return", {
            available: false,
            reason: 'AI features disabled'
          });
        case 1:
          _context.prev = 1;
          controller = new AbortController();
          timeoutId = setTimeout(function () {
            return controller.abort();
          }, aiConfig.timeout);
          _context.next = 2;
          return fetch("".concat(aiConfig.baseUrl, "/health/"), {
            method: 'GET',
            signal: controller.signal,
            headers: {
              'Content-Type': 'application/json'
            }
          });
        case 2:
          response = _context.sent;
          clearTimeout(timeoutId);
          if (!response.ok) {
            _context.next = 3;
            break;
          }
          return _context.abrupt("return", {
            available: true,
            status: response.status
          });
        case 3:
          return _context.abrupt("return", {
            available: false,
            reason: "HTTP ".concat(response.status)
          });
        case 4:
          _context.next = 7;
          break;
        case 5:
          _context.prev = 5;
          _t = _context["catch"](1);
          if (!(_t.name === 'AbortError')) {
            _context.next = 6;
            break;
          }
          return _context.abrupt("return", {
            available: false,
            reason: 'Timeout'
          });
        case 6:
          return _context.abrupt("return", {
            available: false,
            reason: _t.message
          });
        case 7:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[1, 5]]);
  }));
  return function checkAIServiceAvailability() {
    return _ref.apply(this, arguments);
  };
}()));

// Error handling configuration
var errorConfig = {
  // Log levels: 'error', 'warn', 'info', 'debug'
  logLevel:  false ? 0 : 'warn',
  // Error reporting
  reportErrors: false,
  // Set to true to enable error reporting

  // User-facing messages
  messages: {
    serviceUnavailable: 'AI suggestions are temporarily unavailable. Using basic recommendations.',
    networkError: 'Unable to connect to AI service. Check your internet connection.',
    timeout: 'AI service is taking too long to respond. Using cached results.',
    fallback: 'Using offline AI suggestions.'
  }
};

// Development helpers
var devConfig = {
  mockResponses: "production" === 'development',
  debugMode: {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_25412_JVTRZOZECLVMQHVG","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11708_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_DEBUG === 'true',
  verboseLogging: {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_25412_JVTRZOZECLVMQHVG","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11708_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_VERBOSE === 'true'
};
/* harmony default export */ const config_aiConfig = ((/* unused pure expression or super */ null && (aiConfig)));
;// ./src/services/aiWebSocketService.js


/**
 * AI WebSocket Service
 * Handles real-time AI suggestions via WebSocket connection
 */
var AIWebSocketService = /*#__PURE__*/function () {
  function AIWebSocketService() {
    (0,classCallCheck/* default */.A)(this, AIWebSocketService);
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.listeners = new Map();
    this.messageQueue = [];
    this.subscriptions = new Set();

    // Get WebSocket URL
    var protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    var host = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_25412_JVTRZOZECLVMQHVG","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11708_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.1","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_WS_HOST || window.location.host;
    this.wsUrl = "".concat(protocol, "//").concat(host, "/ws/ai-suggestions/");
  }

  /**
   * Connect to AI suggestions WebSocket
   */
  return (0,createClass/* default */.A)(AIWebSocketService, [{
    key: "connect",
    value: function connect() {
      var _this = this;
      if (this.isConnected || this.ws) {
        return Promise.resolve();
      }
      return new Promise(function (resolve, reject) {
        try {
          _this.ws = new WebSocket(_this.wsUrl);
          _this.ws.onopen = function () {
            console.log('AI WebSocket connected');
            _this.isConnected = true;
            _this.reconnectAttempts = 0;

            // Process queued messages
            _this.processMessageQueue();

            // Emit connection event
            _this.emit('connected');
            resolve();
          };
          _this.ws.onmessage = function (event) {
            try {
              var data = JSON.parse(event.data);
              _this.handleMessage(data);
            } catch (error) {
              console.error('Error parsing AI WebSocket message:', error);
            }
          };
          _this.ws.onclose = function (event) {
            console.log('AI WebSocket disconnected:', event.code, event.reason);
            _this.isConnected = false;
            _this.ws = null;

            // Emit disconnection event
            _this.emit('disconnected', {
              code: event.code,
              reason: event.reason
            });

            // Attempt reconnection if not intentional
            if (event.code !== 1000 && _this.reconnectAttempts < _this.maxReconnectAttempts) {
              _this.scheduleReconnect();
            }
          };
          _this.ws.onerror = function (error) {
            console.error('AI WebSocket error:', error);
            _this.emit('error', error);
            reject(error);
          };
        } catch (error) {
          console.error('Error creating AI WebSocket:', error);
          reject(error);
        }
      });
    }

    /**
     * Disconnect from WebSocket
     */
  }, {
    key: "disconnect",
    value: function disconnect() {
      if (this.ws) {
        this.ws.close(1000, 'Client disconnect');
        this.ws = null;
        this.isConnected = false;
      }
    }

    /**
     * Schedule reconnection attempt
     */
  }, {
    key: "scheduleReconnect",
    value: function scheduleReconnect() {
      var _this2 = this;
      this.reconnectAttempts++;
      var delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);
      console.log("Scheduling AI WebSocket reconnect attempt ".concat(this.reconnectAttempts, " in ").concat(delay, "ms"));
      setTimeout(function () {
        if (!_this2.isConnected) {
          _this2.connect()["catch"](function (error) {
            console.error('AI WebSocket reconnect failed:', error);
          });
        }
      }, delay);
    }

    /**
     * Send message to WebSocket
     */
  }, {
    key: "send",
    value: function send(message) {
      if (this.isConnected && this.ws) {
        this.ws.send(JSON.stringify(message));
      } else {
        // Queue message for later
        this.messageQueue.push(message);

        // Try to connect if not connected
        if (!this.isConnected) {
          this.connect();
        }
      }
    }

    /**
     * Process queued messages
     */
  }, {
    key: "processMessageQueue",
    value: function processMessageQueue() {
      while (this.messageQueue.length > 0) {
        var message = this.messageQueue.shift();
        this.send(message);
      }
    }

    /**
     * Handle incoming WebSocket messages
     */
  }, {
    key: "handleMessage",
    value: function handleMessage(data) {
      var type = data.type;
      switch (type) {
        case 'connection_established':
          console.log('AI WebSocket connection established');
          break;
        case 'layout_suggestions':
          this.emit('layoutSuggestions', data.suggestions);
          break;
        case 'component_combinations':
          this.emit('componentCombinations', data.suggestions);
          break;
        case 'app_analysis':
          this.emit('appAnalysis', data.analysis);
          break;
        case 'layout_suggestions_broadcast':
          this.emit('layoutSuggestionsBroadcast', data.suggestions);
          break;
        case 'component_combinations_broadcast':
          this.emit('componentCombinationsBroadcast', data.suggestions);
          break;
        case 'ai_suggestion_update':
          this.emit('aiSuggestionUpdate', data);
          break;
        case 'error':
          console.error('AI WebSocket error:', data.message);
          this.emit('error', new Error(data.message));
          break;
        case 'pong':
          this.emit('pong', data);
          break;
        default:
          console.log('Unknown AI WebSocket message type:', type, data);
      }
    }

    /**
     * Request layout suggestions
     */
  }, {
    key: "requestLayoutSuggestions",
    value: function requestLayoutSuggestions(components) {
      var layouts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
      var context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var broadcast = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
      this.send({
        type: 'get_layout_suggestions',
        components: components,
        layouts: layouts,
        context: context,
        broadcast: broadcast,
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Request component combinations
     */
  }, {
    key: "requestComponentCombinations",
    value: function requestComponentCombinations(components) {
      var selectedComponent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
      var context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      var broadcast = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
      this.send({
        type: 'get_component_combinations',
        components: components,
        selected_component: selectedComponent,
        context: context,
        broadcast: broadcast,
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Request app structure analysis
     */
  }, {
    key: "requestAppAnalysis",
    value: function requestAppAnalysis(components) {
      var layouts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
      this.send({
        type: 'analyze_app_structure',
        components: components,
        layouts: layouts,
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Subscribe to AI updates
     */
  }, {
    key: "subscribeToUpdates",
    value: function subscribeToUpdates() {
      var subscriptionType = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'all';
      this.subscriptions.add(subscriptionType);
      this.send({
        type: 'subscribe_to_updates',
        subscription_type: subscriptionType,
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Send ping to keep connection alive
     */
  }, {
    key: "ping",
    value: function ping() {
      this.send({
        type: 'ping',
        timestamp: new Date().toISOString()
      });
    }

    /**
     * Add event listener
     */
  }, {
    key: "addEventListener",
    value: function addEventListener(event, callback) {
      if (!this.listeners.has(event)) {
        this.listeners.set(event, new Set());
      }
      this.listeners.get(event).add(callback);
    }

    /**
     * Remove event listener
     */
  }, {
    key: "removeEventListener",
    value: function removeEventListener(event, callback) {
      if (this.listeners.has(event)) {
        this.listeners.get(event)["delete"](callback);
      }
    }

    /**
     * Emit event to listeners
     */
  }, {
    key: "emit",
    value: function emit(event, data) {
      if (this.listeners.has(event)) {
        this.listeners.get(event).forEach(function (callback) {
          try {
            callback(data);
          } catch (error) {
            console.error("Error in AI WebSocket event listener for ".concat(event, ":"), error);
          }
        });
      }
    }

    /**
     * Get connection status
     */
  }, {
    key: "getStatus",
    value: function getStatus() {
      return {
        connected: this.isConnected,
        reconnectAttempts: this.reconnectAttempts,
        subscriptions: Array.from(this.subscriptions),
        queuedMessages: this.messageQueue.length
      };
    }
  }]);
}(); // Create singleton instance
var aiWebSocketService = new AIWebSocketService();

// Auto-connect when service is imported
if (typeof window !== 'undefined') {
  // Connect after a short delay to allow app initialization
  setTimeout(function () {
    aiWebSocketService.connect()["catch"](function (error) {
      console.warn('Initial AI WebSocket connection failed:', error);
    });
  }, 1000);
}
/* harmony default export */ const services_aiWebSocketService = (aiWebSocketService);
;// ./src/services/aiDesignService.js




/**
 * AI Design Service
 * Enhanced AI service for layout suggestions and component combinations
 */

// Import configuration


// Import WebSocket service

var AIDesignService = /*#__PURE__*/function () {
  function AIDesignService() {
    (0,classCallCheck/* default */.A)(this, AIDesignService);
    this.baseUrl = aiConfig.baseUrl;
    this.cache = new Map();
    this.cacheTimeout = aiConfig.cacheTimeout;
    this.useWebSocket = aiConfig.websocketEnabled;
    this.wsService = services_aiWebSocketService;
    this.enabled = isAIEnabled();

    // Setup WebSocket event listeners
    this.setupWebSocketListeners();
  }

  /**
   * Setup WebSocket event listeners
   */
  return (0,createClass/* default */.A)(AIDesignService, [{
    key: "setupWebSocketListeners",
    value: function setupWebSocketListeners() {
      var _this = this;
      this.wsService.addEventListener('layoutSuggestions', function (suggestions) {
        // Cache WebSocket results
        var cacheKey = 'ws_layout_suggestions';
        _this.cache.set(cacheKey, {
          data: {
            suggestions: suggestions,
            status: 'success'
          },
          timestamp: Date.now()
        });
      });
      this.wsService.addEventListener('componentCombinations', function (suggestions) {
        // Cache WebSocket results
        var cacheKey = 'ws_component_combinations';
        _this.cache.set(cacheKey, {
          data: {
            suggestions: suggestions,
            status: 'success'
          },
          timestamp: Date.now()
        });
      });
      this.wsService.addEventListener('error', function (error) {
        if (shouldShowWarnings()) {
          console.warn('AI WebSocket error, falling back to HTTP:', error);
        }
      });
    }

    /**
     * Generate layout suggestions based on app structure
     * @param {Array} components - Current components in the app
     * @param {Array} layouts - Existing layouts (optional)
     * @param {Object} context - Additional context (optional)
     * @returns {Promise<Object>} Layout suggestions response
     */
  }, {
    key: "generateLayoutSuggestions",
    value: (function () {
      var _generateLayoutSuggestions = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(components) {
        var layouts,
          context,
          cacheKey,
          cached,
          response,
          data,
          _args = arguments,
          _t,
          _t2;
        return regenerator_default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              layouts = _args.length > 1 && _args[1] !== undefined ? _args[1] : [];
              context = _args.length > 2 && _args[2] !== undefined ? _args[2] : {};
              if (this.enabled) {
                _context.next = 1;
                break;
              }
              return _context.abrupt("return", this._getFallbackLayoutSuggestions(components));
            case 1:
              cacheKey = "layout_".concat(JSON.stringify({
                components: components,
                layouts: layouts,
                context: context
              })); // Check cache first
              if (!this.cache.has(cacheKey)) {
                _context.next = 2;
                break;
              }
              cached = this.cache.get(cacheKey);
              if (!(Date.now() - cached.timestamp < this.cacheTimeout)) {
                _context.next = 2;
                break;
              }
              return _context.abrupt("return", cached.data);
            case 2:
              if (!(this.useWebSocket && this.wsService.getStatus().connected)) {
                _context.next = 6;
                break;
              }
              _context.prev = 3;
              _context.next = 4;
              return this._getLayoutSuggestionsViaWebSocket(components, layouts, context, cacheKey);
            case 4:
              return _context.abrupt("return", _context.sent);
            case 5:
              _context.prev = 5;
              _t = _context["catch"](3);
              if (shouldShowWarnings()) {
                console.warn('WebSocket request failed, falling back to HTTP:', _t);
              }
            case 6:
              _context.prev = 6;
              _context.next = 7;
              return fetch("".concat(this.baseUrl, "/layout-suggestions/"), {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': this._getAuthHeader()
                },
                body: JSON.stringify({
                  components: components,
                  layouts: layouts,
                  context: context
                })
              });
            case 7:
              response = _context.sent;
              if (response.ok) {
                _context.next = 8;
                break;
              }
              throw new Error("HTTP error! status: ".concat(response.status));
            case 8:
              _context.next = 9;
              return response.json();
            case 9:
              data = _context.sent;
              // Cache the result
              this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
              });
              return _context.abrupt("return", data);
            case 10:
              _context.prev = 10;
              _t2 = _context["catch"](6);
              if (shouldShowWarnings()) {
                console.warn('AI service unavailable for layout suggestions:', _t2.message);
              }

              // Return fallback suggestions
              return _context.abrupt("return", this._getFallbackLayoutSuggestions(components));
            case 11:
            case "end":
              return _context.stop();
          }
        }, _callee, this, [[3, 5], [6, 10]]);
      }));
      function generateLayoutSuggestions(_x) {
        return _generateLayoutSuggestions.apply(this, arguments);
      }
      return generateLayoutSuggestions;
    }()
    /**
     * Get layout suggestions via WebSocket
     * @private
     */
    )
  }, {
    key: "_getLayoutSuggestionsViaWebSocket",
    value: (function () {
      var _getLayoutSuggestionsViaWebSocket2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(components, layouts, context, cacheKey) {
        var _this2 = this;
        return regenerator_default().wrap(function (_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              return _context2.abrupt("return", new Promise(function (resolve, reject) {
                var timeout = setTimeout(function () {
                  _this2.wsService.removeEventListener('layoutSuggestions', _responseHandler);
                  reject(new Error('WebSocket request timeout'));
                }, 10000); // 10 second timeout

                var _responseHandler = function responseHandler(suggestions) {
                  clearTimeout(timeout);
                  _this2.wsService.removeEventListener('layoutSuggestions', _responseHandler);
                  var data = {
                    suggestions: suggestions,
                    status: 'success'
                  };

                  // Cache the result
                  _this2.cache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                  });
                  resolve(data);
                };
                _this2.wsService.addEventListener('layoutSuggestions', _responseHandler);
                _this2.wsService.requestLayoutSuggestions(components, layouts, context);
              }));
            case 1:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }));
      function _getLayoutSuggestionsViaWebSocket(_x2, _x3, _x4, _x5) {
        return _getLayoutSuggestionsViaWebSocket2.apply(this, arguments);
      }
      return _getLayoutSuggestionsViaWebSocket;
    }()
    /**
     * Generate component combination suggestions
     * @param {Array} components - Current components in the app
     * @param {Object} selectedComponent - Currently selected component (optional)
     * @param {Object} context - Additional context (optional)
     * @returns {Promise<Object>} Component combination suggestions response
     */
    )
  }, {
    key: "generateComponentCombinations",
    value: (function () {
      var _generateComponentCombinations = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3(components) {
        var selectedComponent,
          context,
          cacheKey,
          cached,
          response,
          data,
          _args3 = arguments,
          _t3,
          _t4;
        return regenerator_default().wrap(function (_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              selectedComponent = _args3.length > 1 && _args3[1] !== undefined ? _args3[1] : null;
              context = _args3.length > 2 && _args3[2] !== undefined ? _args3[2] : {};
              if (this.enabled) {
                _context3.next = 1;
                break;
              }
              return _context3.abrupt("return", this._getFallbackCombinationSuggestions(components, selectedComponent));
            case 1:
              cacheKey = "combinations_".concat(JSON.stringify({
                components: components,
                selectedComponent: selectedComponent,
                context: context
              })); // Check cache first
              if (!this.cache.has(cacheKey)) {
                _context3.next = 2;
                break;
              }
              cached = this.cache.get(cacheKey);
              if (!(Date.now() - cached.timestamp < this.cacheTimeout)) {
                _context3.next = 2;
                break;
              }
              return _context3.abrupt("return", cached.data);
            case 2:
              if (!(this.useWebSocket && this.wsService.getStatus().connected)) {
                _context3.next = 6;
                break;
              }
              _context3.prev = 3;
              _context3.next = 4;
              return this._getComponentCombinationsViaWebSocket(components, selectedComponent, context, cacheKey);
            case 4:
              return _context3.abrupt("return", _context3.sent);
            case 5:
              _context3.prev = 5;
              _t3 = _context3["catch"](3);
              if (shouldShowWarnings()) {
                console.warn('WebSocket request failed, falling back to HTTP:', _t3);
              }
            case 6:
              _context3.prev = 6;
              _context3.next = 7;
              return fetch("".concat(this.baseUrl, "/component-combinations/"), {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': this._getAuthHeader()
                },
                body: JSON.stringify({
                  components: components,
                  selected_component: selectedComponent,
                  context: context
                })
              });
            case 7:
              response = _context3.sent;
              if (response.ok) {
                _context3.next = 8;
                break;
              }
              throw new Error("HTTP error! status: ".concat(response.status));
            case 8:
              _context3.next = 9;
              return response.json();
            case 9:
              data = _context3.sent;
              // Cache the result
              this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
              });
              return _context3.abrupt("return", data);
            case 10:
              _context3.prev = 10;
              _t4 = _context3["catch"](6);
              if (shouldShowWarnings()) {
                console.warn('AI service unavailable for component combinations:', _t4.message);
              }

              // Return fallback suggestions
              return _context3.abrupt("return", this._getFallbackCombinationSuggestions(components, selectedComponent));
            case 11:
            case "end":
              return _context3.stop();
          }
        }, _callee3, this, [[3, 5], [6, 10]]);
      }));
      function generateComponentCombinations(_x6) {
        return _generateComponentCombinations.apply(this, arguments);
      }
      return generateComponentCombinations;
    }()
    /**
     * Get component combinations via WebSocket
     * @private
     */
    )
  }, {
    key: "_getComponentCombinationsViaWebSocket",
    value: (function () {
      var _getComponentCombinationsViaWebSocket2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee4(components, selectedComponent, context, cacheKey) {
        var _this3 = this;
        return regenerator_default().wrap(function (_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              return _context4.abrupt("return", new Promise(function (resolve, reject) {
                var timeout = setTimeout(function () {
                  _this3.wsService.removeEventListener('componentCombinations', _responseHandler2);
                  reject(new Error('WebSocket request timeout'));
                }, 10000); // 10 second timeout

                var _responseHandler2 = function responseHandler(suggestions) {
                  clearTimeout(timeout);
                  _this3.wsService.removeEventListener('componentCombinations', _responseHandler2);
                  var data = {
                    suggestions: suggestions,
                    status: 'success'
                  };

                  // Cache the result
                  _this3.cache.set(cacheKey, {
                    data: data,
                    timestamp: Date.now()
                  });
                  resolve(data);
                };
                _this3.wsService.addEventListener('componentCombinations', _responseHandler2);
                _this3.wsService.requestComponentCombinations(components, selectedComponent, context);
              }));
            case 1:
            case "end":
              return _context4.stop();
          }
        }, _callee4);
      }));
      function _getComponentCombinationsViaWebSocket(_x7, _x8, _x9, _x0) {
        return _getComponentCombinationsViaWebSocket2.apply(this, arguments);
      }
      return _getComponentCombinationsViaWebSocket;
    }()
    /**
     * Analyze app structure
     * @param {Array} components - Current components in the app
     * @param {Array} layouts - Existing layouts (optional)
     * @returns {Promise<Object>} App structure analysis response
     */
    )
  }, {
    key: "analyzeAppStructure",
    value: (function () {
      var _analyzeAppStructure = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee5(components) {
        var layouts,
          cacheKey,
          cached,
          response,
          data,
          fallbackData,
          _args5 = arguments,
          _t5;
        return regenerator_default().wrap(function (_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              layouts = _args5.length > 1 && _args5[1] !== undefined ? _args5[1] : [];
              if (this.enabled) {
                _context5.next = 1;
                break;
              }
              return _context5.abrupt("return", this._getBasicAnalysis(components));
            case 1:
              cacheKey = "analysis_".concat(JSON.stringify({
                components: components,
                layouts: layouts
              })); // Check cache first
              if (!this.cache.has(cacheKey)) {
                _context5.next = 2;
                break;
              }
              cached = this.cache.get(cacheKey);
              if (!(Date.now() - cached.timestamp < this.cacheTimeout)) {
                _context5.next = 2;
                break;
              }
              return _context5.abrupt("return", cached.data);
            case 2:
              _context5.prev = 2;
              _context5.next = 3;
              return fetch("".concat(this.baseUrl, "/analyze-structure/"), {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': this._getAuthHeader()
                },
                body: JSON.stringify({
                  components: components,
                  layouts: layouts
                })
              });
            case 3:
              response = _context5.sent;
              if (response.ok) {
                _context5.next = 4;
                break;
              }
              throw new Error("HTTP error! status: ".concat(response.status));
            case 4:
              _context5.next = 5;
              return response.json();
            case 5:
              data = _context5.sent;
              // Cache the result
              this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
              });
              return _context5.abrupt("return", data);
            case 6:
              _context5.prev = 6;
              _t5 = _context5["catch"](2);
              console.warn('AI service unavailable, using basic analysis:', _t5.message);

              // Return basic analysis as fallback
              fallbackData = this._getBasicAnalysis(components); // Cache the fallback result for a shorter time
              this.cache.set(cacheKey, {
                data: fallbackData,
                timestamp: Date.now()
              });
              return _context5.abrupt("return", fallbackData);
            case 7:
            case "end":
              return _context5.stop();
          }
        }, _callee5, this, [[2, 6]]);
      }));
      function analyzeAppStructure(_x1) {
        return _analyzeAppStructure.apply(this, arguments);
      }
      return analyzeAppStructure;
    }()
    /**
     * Clear cache
     */
    )
  }, {
    key: "clearCache",
    value: function clearCache() {
      this.cache.clear();
    }

    /**
     * Get authentication header
     * @private
     */
  }, {
    key: "_getAuthHeader",
    value: function _getAuthHeader() {
      var token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
      return token ? "Bearer ".concat(token) : '';
    }

    /**
     * Get fallback layout suggestions when API is unavailable
     * @private
     */
  }, {
    key: "_getFallbackLayoutSuggestions",
    value: function _getFallbackLayoutSuggestions(components) {
      var componentCount = components.length;
      var suggestions = [];
      if (componentCount <= 3) {
        suggestions.push({
          id: 'simple_flex',
          name: 'Simple Flexbox Layout',
          description: 'Basic vertical layout for simple apps',
          score: 80,
          explanation: 'Perfect for apps with few components',
          structure: {
            display: 'flex',
            flexDirection: 'column'
          }
        });
      }
      if (componentCount > 3) {
        suggestions.push({
          id: 'grid_layout',
          name: 'Grid Layout',
          description: 'Organized grid for multiple components',
          score: 85,
          explanation: 'Grid layout works well for organizing many components',
          structure: {
            display: 'grid',
            gap: '16px'
          }
        });
      }
      suggestions.push({
        id: 'header_footer',
        name: 'Header-Footer Layout',
        description: 'Classic layout with header and footer',
        score: 75,
        explanation: 'Traditional layout suitable for most applications',
        structure: {
          header: true,
          footer: true
        }
      });
      return {
        suggestions: suggestions,
        status: 'fallback',
        component_count: componentCount
      };
    }

    /**
     * Get fallback component combination suggestions
     * @private
     */
  }, {
    key: "_getFallbackCombinationSuggestions",
    value: function _getFallbackCombinationSuggestions(components, selectedComponent) {
      var suggestions = [];
      var componentTypes = components.map(function (c) {
        return c.type;
      });
      if (selectedComponent) {
        var type = selectedComponent.type;
        if (type === 'button' && !componentTypes.includes('form')) {
          suggestions.push({
            id: 'button_form',
            name: 'Button + Form',
            description: 'Add a form to go with your button',
            score: 70,
            components: ['button', 'form'],
            missing_components: ['form']
          });
        }
        if (type === 'text' && !componentTypes.includes('image')) {
          suggestions.push({
            id: 'text_image',
            name: 'Text + Image',
            description: 'Add an image to complement your text',
            score: 65,
            components: ['text', 'image'],
            missing_components: ['image']
          });
        }
      }
      if (!componentTypes.includes('header')) {
        suggestions.push({
          id: 'add_header',
          name: 'Add Header',
          description: 'Every app needs a header for navigation',
          score: 80,
          components: ['header'],
          missing_components: ['header']
        });
      }
      return {
        suggestions: suggestions,
        status: 'fallback',
        component_count: components.length
      };
    }

    /**
     * Get basic app structure analysis
     * @private
     */
  }, {
    key: "_getBasicAnalysis",
    value: function _getBasicAnalysis(components) {
      var componentTypes = {};
      components.forEach(function (comp) {
        var type = comp.type || 'unknown';
        componentTypes[type] = (componentTypes[type] || 0) + 1;
      });
      return {
        analysis: {
          component_count: components.length,
          component_types: componentTypes,
          has_navigation: Object.keys(componentTypes).some(function (type) {
            return ['header', 'nav', 'menu'].includes(type);
          }),
          has_forms: Object.keys(componentTypes).some(function (type) {
            return ['form', 'input', 'button'].includes(type);
          }),
          has_media: Object.keys(componentTypes).some(function (type) {
            return ['image', 'video', 'gallery'].includes(type);
          }),
          complexity_score: Object.keys(componentTypes).length * 2 + components.length,
          app_type: 'general'
        },
        status: 'basic'
      };
    }
  }]);
}(); // Create and export singleton instance
var aiDesignService = new AIDesignService();
/* harmony default export */ const services_aiDesignService = (aiDesignService);
// EXTERNAL MODULE: ./src/redux/actions.js
var actions = __webpack_require__(81616);
;// ./src/hooks/useAIDesignSuggestions.js



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






/**
 * Custom hook for managing AI design suggestions
 * Provides layout suggestions, component combinations, and app analysis
 */
var useAIDesignSuggestions = function useAIDesignSuggestions() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$autoRefresh = options.autoRefresh,
    autoRefresh = _options$autoRefresh === void 0 ? true : _options$autoRefresh,
    _options$refreshInter = options.refreshInterval,
    refreshInterval = _options$refreshInter === void 0 ? 30000 : _options$refreshInter,
    _options$enableCache = options.enableCache,
    enableCache = _options$enableCache === void 0 ? true : _options$enableCache,
    _options$context = options.context,
    context = _options$context === void 0 ? {} : _options$context;
  var dispatch = (0,react_redux/* useDispatch */.wA)();

  // Get app state from Redux
  var components = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$app;
    return ((_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || state.components || [];
  });
  var layouts = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$app2;
    return ((_state$app2 = state.app) === null || _state$app2 === void 0 ? void 0 : _state$app2.layouts) || state.layouts || [];
  });
  var selectedComponent = (0,react_redux/* useSelector */.d4)(function (state) {
    var _state$ui;
    return ((_state$ui = state.ui) === null || _state$ui === void 0 ? void 0 : _state$ui.selectedComponent) || null;
  });

  // Local state
  var _useState = (0,react.useState)({
      layout: [],
      combinations: [],
      analysis: null
    }),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    suggestions = _useState2[0],
    setSuggestions = _useState2[1];
  var _useState3 = (0,react.useState)({
      layout: false,
      combinations: false,
      analysis: false
    }),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react.useState)(null),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    error = _useState6[0],
    setError = _useState6[1];
  var _useState7 = (0,react.useState)(null),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    lastRefresh = _useState8[0],
    setLastRefresh = _useState8[1];

  // Refs for cleanup
  var refreshIntervalRef = (0,react.useRef)(null);
  var abortControllerRef = (0,react.useRef)(null);

  // Load all suggestions
  var loadSuggestions = (0,react.useCallback)(/*#__PURE__*/(0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
    var force,
      _yield$Promise$allSet,
      _yield$Promise$allSet2,
      layoutResponse,
      combinationsResponse,
      analysisResponse,
      layoutSuggestions,
      combinationSuggestions,
      analysis,
      _args = arguments,
      _t;
    return regenerator_default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          force = _args.length > 0 && _args[0] !== undefined ? _args[0] : false;
          if (!(!components || components.length === 0)) {
            _context.next = 1;
            break;
          }
          setSuggestions({
            layout: [],
            combinations: [],
            analysis: null
          });
          return _context.abrupt("return");
        case 1:
          // Abort previous requests
          if (abortControllerRef.current) {
            abortControllerRef.current.abort();
          }
          abortControllerRef.current = new AbortController();
          setError(null);
          setLoading({
            layout: true,
            combinations: true,
            analysis: true
          });
          _context.prev = 2;
          // Clear cache if force refresh
          if (force && enableCache) {
            services_aiDesignService.clearCache();
          }

          // Load all suggestions in parallel
          _context.next = 3;
          return Promise.allSettled([services_aiDesignService.generateLayoutSuggestions(components, layouts, context), services_aiDesignService.generateComponentCombinations(components, selectedComponent, context), services_aiDesignService.analyzeAppStructure(components, layouts)]);
        case 3:
          _yield$Promise$allSet = _context.sent;
          _yield$Promise$allSet2 = (0,slicedToArray/* default */.A)(_yield$Promise$allSet, 3);
          layoutResponse = _yield$Promise$allSet2[0];
          combinationsResponse = _yield$Promise$allSet2[1];
          analysisResponse = _yield$Promise$allSet2[2];
          // Process layout suggestions
          layoutSuggestions = layoutResponse.status === 'fulfilled' ? layoutResponse.value.suggestions || [] : []; // Process combination suggestions
          combinationSuggestions = combinationsResponse.status === 'fulfilled' ? combinationsResponse.value.suggestions || [] : []; // Process analysis
          analysis = analysisResponse.status === 'fulfilled' ? analysisResponse.value.analysis || null : null;
          setSuggestions({
            layout: layoutSuggestions,
            combinations: combinationSuggestions,
            analysis: analysis
          });
          setLastRefresh(new Date());

          // Log any errors
          [layoutResponse, combinationsResponse, analysisResponse].forEach(function (response, index) {
            if (response.status === 'rejected') {
              var names = ['layout', 'combinations', 'analysis'];
              console.warn("Failed to load ".concat(names[index], " suggestions:"), response.reason);
            }
          });
          _context.next = 5;
          break;
        case 4:
          _context.prev = 4;
          _t = _context["catch"](2);
          if (_t.name !== 'AbortError') {
            setError("Failed to load suggestions: ".concat(_t.message));
          }
        case 5:
          _context.prev = 5;
          setLoading({
            layout: false,
            combinations: false,
            analysis: false
          });
          return _context.finish(5);
        case 6:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[2, 4, 5, 6]]);
  })), [components, layouts, selectedComponent, context, enableCache]);

  // Load specific suggestion type
  var loadLayoutSuggestions = (0,react.useCallback)(/*#__PURE__*/(0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2() {
    var response, _t2;
    return regenerator_default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if (!(!components || components.length === 0)) {
            _context2.next = 1;
            break;
          }
          return _context2.abrupt("return");
        case 1:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: true
            });
          });
          setError(null);
          _context2.prev = 2;
          _context2.next = 3;
          return services_aiDesignService.generateLayoutSuggestions(components, layouts, context);
        case 3:
          response = _context2.sent;
          setSuggestions(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: response.suggestions || []
            });
          });
          _context2.next = 5;
          break;
        case 4:
          _context2.prev = 4;
          _t2 = _context2["catch"](2);
          setError("Failed to load layout suggestions: ".concat(_t2.message));
        case 5:
          _context2.prev = 5;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: false
            });
          });
          return _context2.finish(5);
        case 6:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[2, 4, 5, 6]]);
  })), [components, layouts, context]);
  var loadCombinationSuggestions = (0,react.useCallback)(/*#__PURE__*/(0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3() {
    var response, _t3;
    return regenerator_default().wrap(function (_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          if (!(!components || components.length === 0)) {
            _context3.next = 1;
            break;
          }
          return _context3.abrupt("return");
        case 1:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: true
            });
          });
          setError(null);
          _context3.prev = 2;
          _context3.next = 3;
          return services_aiDesignService.generateComponentCombinations(components, selectedComponent, context);
        case 3:
          response = _context3.sent;
          setSuggestions(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: response.suggestions || []
            });
          });
          _context3.next = 5;
          break;
        case 4:
          _context3.prev = 4;
          _t3 = _context3["catch"](2);
          setError("Failed to load combination suggestions: ".concat(_t3.message));
        case 5:
          _context3.prev = 5;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: false
            });
          });
          return _context3.finish(5);
        case 6:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[2, 4, 5, 6]]);
  })), [components, selectedComponent, context]);

  // Apply layout suggestion
  var applyLayoutSuggestion = (0,react.useCallback)(function (suggestion) {
    try {
      // This would integrate with your layout system
      // For now, we'll dispatch a generic action
      console.log('Applying layout suggestion:', suggestion);

      // You could dispatch a specific action here
      // dispatch(applyLayout(suggestion));

      return true;
    } catch (err) {
      setError("Failed to apply layout suggestion: ".concat(err.message));
      return false;
    }
  }, []);

  // Apply component combination suggestion
  var applyComponentCombination = (0,react.useCallback)(function (suggestion) {
    try {
      if (suggestion.missing_components && suggestion.missing_components.length > 0) {
        // Add missing components
        suggestion.missing_components.forEach(function (componentType) {
          var newComponent = {
            type: componentType,
            props: {},
            id: "".concat(componentType, "-").concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9))
          };
          dispatch((0,actions/* addComponent */.X8)(newComponent.type, newComponent.props));
        });
      }
      console.log('Applied component combination:', suggestion);
      return true;
    } catch (err) {
      setError("Failed to apply component combination: ".concat(err.message));
      return false;
    }
  }, [dispatch]);

  // Refresh suggestions
  var refresh = (0,react.useCallback)(function () {
    loadSuggestions(true);
  }, [loadSuggestions]);

  // Clear error
  var clearError = (0,react.useCallback)(function () {
    setError(null);
  }, []);

  // Setup auto-refresh
  (0,react.useEffect)(function () {
    if (autoRefresh && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(function () {
        loadSuggestions();
      }, refreshInterval);
      return function () {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval]); // Removed loadSuggestions to prevent infinite re-renders

  // Load suggestions when components change (but not on every loadSuggestions change)
  (0,react.useEffect)(function () {
    loadSuggestions();
  }, [components.length, selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.id]); // Only depend on specific values that should trigger reload

  // Cleanup on unmount
  (0,react.useEffect)(function () {
    return function () {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);
  return {
    // Data
    suggestions: suggestions,
    loading: loading,
    error: error,
    lastRefresh: lastRefresh,
    // Actions
    loadSuggestions: loadSuggestions,
    loadLayoutSuggestions: loadLayoutSuggestions,
    loadCombinationSuggestions: loadCombinationSuggestions,
    applyLayoutSuggestion: applyLayoutSuggestion,
    applyComponentCombination: applyComponentCombination,
    refresh: refresh,
    clearError: clearError,
    // Computed values
    hasLayoutSuggestions: suggestions.layout.length > 0,
    hasCombinationSuggestions: suggestions.combinations.length > 0,
    hasAnalysis: suggestions.analysis !== null,
    isLoading: loading.layout || loading.combinations || loading.analysis,
    // Component counts for display
    componentCount: components.length,
    layoutCount: layouts.length,
    selectedComponentType: (selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.type) || null
  };
};
/* harmony default export */ const hooks_useAIDesignSuggestions = (useAIDesignSuggestions);

/***/ }),

/***/ 34816:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   addComponent: () => (/* binding */ addComponent),
/* harmony export */   addLayout: () => (/* binding */ addLayout),
/* harmony export */   addTheme: () => (/* binding */ addTheme),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   removeComponent: () => (/* binding */ removeComponent),
/* harmony export */   removeLayout: () => (/* binding */ removeLayout),
/* harmony export */   removeTheme: () => (/* binding */ removeTheme),
/* harmony export */   setActiveTheme: () => (/* binding */ setActiveTheme),
/* harmony export */   setCurrentView: () => (/* binding */ setCurrentView),
/* harmony export */   togglePreviewMode: () => (/* binding */ togglePreviewMode),
/* harmony export */   toggleSidebar: () => (/* binding */ toggleSidebar),
/* harmony export */   updateComponent: () => (/* binding */ updateComponent),
/* harmony export */   updateLayout: () => (/* binding */ updateLayout),
/* harmony export */   updateTheme: () => (/* binding */ updateTheme),
/* harmony export */   websocketConnected: () => (/* binding */ websocketConnected),
/* harmony export */   websocketDisconnected: () => (/* binding */ websocketDisconnected),
/* harmony export */   websocketMessageReceived: () => (/* binding */ websocketMessageReceived)
/* harmony export */ });
/* harmony import */ var _actions_types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(4318);
/**
 * Compatibility layer for the minimal-store.js
 * This file re-exports actions from the main Redux store to maintain backward compatibility
 *
 * IMPORTANT: This file is designed to avoid circular dependencies by directly defining
 * action creators rather than importing them from other files.
 */

// Import action types directly to avoid circular dependencies


// Define action creators directly to avoid circular dependencies
// Component actions
var addComponent = function addComponent(component) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_COMPONENT */ .oz || 'ADD_COMPONENT',
    payload: component
  };
};
var updateComponent = function updateComponent(componentId, props) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_COMPONENT */ .ei || 'UPDATE_COMPONENT',
    payload: {
      id: componentId,
      props: props
    }
  };
};
var removeComponent = function removeComponent(componentId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_COMPONENT */ .xS || 'REMOVE_COMPONENT',
    payload: {
      id: componentId
    }
  };
};

// Layout actions
var addLayout = function addLayout(layout) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_LAYOUT */ .vs || 'ADD_LAYOUT',
    payload: layout
  };
};
var updateLayout = function updateLayout(layoutId, props) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_LAYOUT */ .Pe || 'UPDATE_LAYOUT',
    payload: {
      id: layoutId,
      props: props
    }
  };
};
var removeLayout = function removeLayout(layoutId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_LAYOUT */ .gV || 'REMOVE_LAYOUT',
    payload: {
      id: layoutId
    }
  };
};

// Theme actions
var addTheme = function addTheme(theme) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .ADD_THEME */ .U_ || 'ADD_THEME',
    payload: theme
  };
};
var updateTheme = function updateTheme(theme) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .UPDATE_THEME */ .gk || 'UPDATE_THEME',
    payload: theme
  };
};
var removeTheme = function removeTheme(themeId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .REMOVE_THEME */ .D || 'REMOVE_THEME',
    payload: {
      id: themeId
    }
  };
};
var setActiveTheme = function setActiveTheme(themeId) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .SET_ACTIVE_THEME */ .wH || 'SET_ACTIVE_THEME',
    payload: themeId
  };
};

// WebSocket actions
var websocketConnected = function websocketConnected() {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WEBSOCKET_CONNECTED */ .Kg || 'WEBSOCKET_CONNECTED'
  };
};
var websocketDisconnected = function websocketDisconnected() {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WEBSOCKET_DISCONNECTED */ .co || 'WEBSOCKET_DISCONNECTED'
  };
};
var websocketMessageReceived = function websocketMessageReceived(message) {
  return {
    type: _actions_types__WEBPACK_IMPORTED_MODULE_0__/* .WS_MESSAGE_RECEIVED */ .ZH || 'WEBSOCKET_MESSAGE_RECEIVED',
    payload: message
  };
};

// UI actions
var toggleSidebar = function toggleSidebar() {
  return {
    type: 'TOGGLE_SIDEBAR'
  };
};
var setCurrentView = function setCurrentView(view) {
  return {
    type: 'SET_CURRENT_VIEW',
    payload: view
  };
};
var togglePreviewMode = function togglePreviewMode() {
  return {
    type: 'TOGGLE_PREVIEW_MODE'
  };
};

// Re-export all actions for backward compatibility


// Export a dummy store for backward compatibility
var dummyStore = {
  getState: function getState() {
    return {};
  },
  dispatch: function dispatch() {},
  subscribe: function subscribe() {
    return function () {};
  }
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dummyStore);

/***/ }),

/***/ 47119:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $l: () => (/* binding */ useEnhancedDragDrop)
/* harmony export */ });
/* unused harmony exports useDragVisualFeedback, useDragReorder */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);




/**
 * Enhanced drag and drop hook with visual feedback and animations
 * @param {Object} options - Configuration options
 * @param {Function} options.onDrop - Callback when item is dropped
 * @param {Function} options.onDragStart - Callback when drag starts
 * @param {Function} options.onDragEnd - Callback when drag ends
 * @param {Function} options.onDragOver - Callback when dragging over
 * @param {Function} options.onDragLeave - Callback when leaving drag area
 * @param {boolean} options.snapToGrid - Enable snap to grid functionality
 * @param {number} options.gridSize - Grid size for snapping
 * @param {boolean} options.showDropZones - Show visual drop zones
 * @param {Array} options.acceptedTypes - Accepted drag data types
 * @returns {Object} Drag and drop state and handlers
 */
var useEnhancedDragDrop = function useEnhancedDragDrop() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var onDrop = options.onDrop,
    onDragStart = options.onDragStart,
    onDragEnd = options.onDragEnd,
    onDragOver = options.onDragOver,
    onDragLeave = options.onDragLeave,
    _options$snapToGrid = options.snapToGrid,
    snapToGrid = _options$snapToGrid === void 0 ? false : _options$snapToGrid,
    _options$gridSize = options.gridSize,
    gridSize = _options$gridSize === void 0 ? 20 : _options$gridSize,
    _options$showDropZone = options.showDropZones,
    showDropZones = _options$showDropZone === void 0 ? true : _options$showDropZone,
    _options$acceptedType = options.acceptedTypes,
    acceptedTypes = _options$acceptedType === void 0 ? ['application/json'] : _options$acceptedType;

  // State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    isDragging = _useState2[0],
    setIsDragging = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    isOver = _useState4[0],
    setIsOver = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    dragData = _useState6[0],
    setDragData = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      x: 0,
      y: 0
    }),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    dropPosition = _useState8[0],
    setDropPosition = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    validDropZone = _useState0[0],
    setValidDropZone = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    dragPreview = _useState10[0],
    setDragPreview = _useState10[1];

  // Refs
  var dropZoneRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
  var dragPreviewRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);

  // Handle drag start
  var handleDragStart = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e, data) {
    setIsDragging(true);
    setDragData(data);

    // Set drag data
    if (data) {
      e.dataTransfer.setData('application/json', JSON.stringify(data));
    }
    e.dataTransfer.effectAllowed = 'copy';

    // Create custom drag preview if provided
    if (dragPreviewRef.current) {
      var preview = dragPreviewRef.current.cloneNode(true);
      preview.style.position = 'absolute';
      preview.style.top = '-1000px';
      preview.style.left = '-1000px';
      preview.style.opacity = '0.8';
      preview.style.transform = 'rotate(5deg) scale(0.9)';
      preview.style.pointerEvents = 'none';
      preview.style.zIndex = '9999';
      document.body.appendChild(preview);
      e.dataTransfer.setDragImage(preview, 50, 25);

      // Clean up preview after drag
      setTimeout(function () {
        if (document.body.contains(preview)) {
          document.body.removeChild(preview);
        }
      }, 0);
    }
    if (onDragStart) {
      onDragStart(e, data);
    }
  }, [onDragStart]);

  // Handle drag end
  var handleDragEnd = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    setIsDragging(false);
    setDragData(null);
    setDropPosition({
      x: 0,
      y: 0
    });
    setValidDropZone(true);
    if (onDragEnd) {
      onDragEnd(e);
    }
  }, [onDragEnd]);

  // Handle drag enter
  var handleDragEnter = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();
    setIsOver(true);
  }, []);

  // Handle drag over
  var handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();

    // Calculate drop position
    if (dropZoneRef.current) {
      var rect = dropZoneRef.current.getBoundingClientRect();
      var x = e.clientX - rect.left;
      var y = e.clientY - rect.top;

      // Apply snap to grid if enabled
      if (snapToGrid) {
        x = Math.round(x / gridSize) * gridSize;
        y = Math.round(y / gridSize) * gridSize;
      }
      setDropPosition({
        x: x,
        y: y
      });
    }

    // Check if drop is valid
    var dragType = e.dataTransfer.types[0];
    var isValidType = acceptedTypes.includes(dragType) || acceptedTypes.length === 0;
    setValidDropZone(isValidType);
    e.dataTransfer.dropEffect = isValidType ? 'copy' : 'none';
    if (onDragOver) {
      onDragOver(e);
    }
  }, [snapToGrid, gridSize, acceptedTypes, onDragOver]);

  // Handle drag leave
  var handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();

    // Only set isOver to false if we're actually leaving the drop zone
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsOver(false);
      setValidDropZone(true);
      if (onDragLeave) {
        onDragLeave(e);
      }
    }
  }, [onDragLeave]);

  // Handle drop
  var handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();
    setIsOver(false);
    setValidDropZone(true);
    try {
      // Get dropped data
      var jsonData = e.dataTransfer.getData('application/json');
      var droppedData = null;
      if (jsonData) {
        droppedData = JSON.parse(jsonData);
      }

      // Calculate final position
      var finalPosition = dropPosition;
      if (snapToGrid) {
        finalPosition = {
          x: Math.round(dropPosition.x / gridSize) * gridSize,
          y: Math.round(dropPosition.y / gridSize) * gridSize
        };
      }
      if (onDrop) {
        onDrop(e, droppedData, finalPosition);
      }
    } catch (error) {
      console.error('Error handling drop:', error);
    }
  }, [dropPosition, snapToGrid, gridSize, onDrop]);

  // Set up event listeners
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var dropZone = dropZoneRef.current;
    if (!dropZone) return;
    dropZone.addEventListener('dragenter', handleDragEnter);
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);
    return function () {
      dropZone.removeEventListener('dragenter', handleDragEnter);
      dropZone.removeEventListener('dragover', handleDragOver);
      dropZone.removeEventListener('dragleave', handleDragLeave);
      dropZone.removeEventListener('drop', handleDrop);
    };
  }, [handleDragEnter, handleDragOver, handleDragLeave, handleDrop]);
  return {
    // State
    isDragging: isDragging,
    isOver: isOver,
    dragData: dragData,
    dropPosition: dropPosition,
    validDropZone: validDropZone,
    // Refs
    dropZoneRef: dropZoneRef,
    dragPreviewRef: dragPreviewRef,
    // Handlers
    handleDragStart: handleDragStart,
    handleDragEnd: handleDragEnd,
    // Utilities
    reset: function reset() {
      setIsDragging(false);
      setIsOver(false);
      setDragData(null);
      setDropPosition({
        x: 0,
        y: 0
      });
      setValidDropZone(true);
    }
  };
};

/**
 * Hook for managing drag visual feedback
 * @param {Object} options - Configuration options
 * @returns {Object} Visual feedback utilities
 */
var useDragVisualFeedback = function useDragVisualFeedback() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$showGhost = options.showGhost,
    showGhost = _options$showGhost === void 0 ? true : _options$showGhost,
    _options$showDropIndi = options.showDropIndicator,
    showDropIndicator = _options$showDropIndi === void 0 ? true : _options$showDropIndi,
    _options$animationDur = options.animationDuration,
    animationDuration = _options$animationDur === void 0 ? 300 : _options$animationDur;
  var _useState11 = useState({
      x: 0,
      y: 0
    }),
    _useState12 = _slicedToArray(_useState11, 2),
    ghostPosition = _useState12[0],
    setGhostPosition = _useState12[1];
  var _useState13 = useState(false),
    _useState14 = _slicedToArray(_useState13, 2),
    showGhostElement = _useState14[0],
    setShowGhostElement = _useState14[1];
  var _useState15 = useState(null),
    _useState16 = _slicedToArray(_useState15, 2),
    dropIndicatorPosition = _useState16[0],
    setDropIndicatorPosition = _useState16[1];

  // Update ghost position during drag
  var updateGhostPosition = useCallback(function (x, y) {
    if (showGhost) {
      setGhostPosition({
        x: x,
        y: y
      });
    }
  }, [showGhost]);

  // Show/hide ghost element
  var toggleGhost = useCallback(function (show) {
    setShowGhostElement(show);
  }, []);

  // Update drop indicator
  var updateDropIndicator = useCallback(function (position) {
    if (showDropIndicator) {
      setDropIndicatorPosition(position);
    }
  }, [showDropIndicator]);

  // Clear all visual feedback
  var clearFeedback = useCallback(function () {
    setShowGhostElement(false);
    setDropIndicatorPosition(null);
    setGhostPosition({
      x: 0,
      y: 0
    });
  }, []);
  return {
    ghostPosition: ghostPosition,
    showGhostElement: showGhostElement,
    dropIndicatorPosition: dropIndicatorPosition,
    updateGhostPosition: updateGhostPosition,
    toggleGhost: toggleGhost,
    updateDropIndicator: updateDropIndicator,
    clearFeedback: clearFeedback
  };
};

/**
 * Hook for managing component reordering with drag and drop
 * @param {Array} items - Array of items to reorder
 * @param {Function} onReorder - Callback when items are reordered
 * @returns {Object} Reordering utilities
 */
var useDragReorder = function useDragReorder(items, onReorder) {
  var _useState17 = useState(null),
    _useState18 = _slicedToArray(_useState17, 2),
    draggedItem = _useState18[0],
    setDraggedItem = _useState18[1];
  var _useState19 = useState(null),
    _useState20 = _slicedToArray(_useState19, 2),
    draggedOverItem = _useState20[0],
    setDraggedOverItem = _useState20[1];
  var _useState21 = useState('after'),
    _useState22 = _slicedToArray(_useState21, 2),
    dropPosition = _useState22[0],
    setDropPosition = _useState22[1]; // 'before' or 'after'

  var handleDragStart = useCallback(function (e, item) {
    setDraggedItem(item);
    e.dataTransfer.setData('application/json', JSON.stringify(item));
    e.dataTransfer.effectAllowed = 'move';
  }, []);
  var handleDragOver = useCallback(function (e, item) {
    e.preventDefault();
    if (draggedItem && draggedItem.id !== item.id) {
      setDraggedOverItem(item);

      // Determine drop position based on mouse position
      var rect = e.currentTarget.getBoundingClientRect();
      var midpoint = rect.top + rect.height / 2;
      setDropPosition(e.clientY < midpoint ? 'before' : 'after');
    }
    e.dataTransfer.dropEffect = 'move';
  }, [draggedItem]);
  var handleDrop = useCallback(function (e, targetItem) {
    e.preventDefault();
    if (draggedItem && targetItem && draggedItem.id !== targetItem.id) {
      var draggedIndex = items.findIndex(function (item) {
        return item.id === draggedItem.id;
      });
      var targetIndex = items.findIndex(function (item) {
        return item.id === targetItem.id;
      });
      if (draggedIndex !== -1 && targetIndex !== -1) {
        var newItems = _toConsumableArray(items);
        var _newItems$splice = newItems.splice(draggedIndex, 1),
          _newItems$splice2 = _slicedToArray(_newItems$splice, 1),
          removed = _newItems$splice2[0];
        var insertIndex = dropPosition === 'before' ? targetIndex : targetIndex + 1;
        newItems.splice(insertIndex, 0, removed);
        if (onReorder) {
          onReorder(newItems);
        }
      }
    }
    setDraggedItem(null);
    setDraggedOverItem(null);
    setDropPosition('after');
  }, [items, draggedItem, dropPosition, onReorder]);
  var handleDragEnd = useCallback(function () {
    setDraggedItem(null);
    setDraggedOverItem(null);
    setDropPosition('after');
  }, []);
  return {
    draggedItem: draggedItem,
    draggedOverItem: draggedOverItem,
    dropPosition: dropPosition,
    handleDragStart: handleDragStart,
    handleDragOver: handleDragOver,
    handleDrop: handleDrop,
    handleDragEnd: handleDragEnd
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (useEnhancedDragDrop)));

/***/ }),

/***/ 48860:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2543);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/**
 * Custom hook for optimizing preview performance
 * Handles virtual rendering, component caching, and performance monitoring
 */
var usePreviewPerformance = function usePreviewPerformance(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$containerHeight = _ref.containerHeight,
    containerHeight = _ref$containerHeight === void 0 ? 600 : _ref$containerHeight,
    _ref$itemHeight = _ref.itemHeight,
    itemHeight = _ref$itemHeight === void 0 ? 100 : _ref$itemHeight,
    _ref$overscan = _ref.overscan,
    overscan = _ref$overscan === void 0 ? 5 : _ref$overscan,
    _ref$enableVirtualiza = _ref.enableVirtualization,
    enableVirtualization = _ref$enableVirtualiza === void 0 ? true : _ref$enableVirtualiza,
    _ref$enablePerformanc = _ref.enablePerformanceMonitoring,
    enablePerformanceMonitoring = _ref$enablePerformanc === void 0 ? true : _ref$enablePerformanc;
  // State for virtualization
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    scrollTop = _useState2[0],
    setScrollTop = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    containerRef = _useState4[0],
    setContainerRef = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      start: 0,
      end: 0
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    visibleRange = _useState6[0],
    setVisibleRange = _useState6[1];

  // Performance monitoring state
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    renderTime = _useState8[0],
    setRenderTime = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(60),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    frameRate = _useState0[0],
    setFrameRate = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    memoryUsage = _useState10[0],
    setMemoryUsage = _useState10[1];

  // Refs for performance tracking
  var renderStartTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);
  var frameCount = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);
  var lastFrameTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(performance.now());
  var componentCache = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(new Map());
  var intersectionObserver = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);

  // Calculate visible items for virtualization
  var calculateVisibleRange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization || !containerRef || components.length === 0) {
      return {
        start: 0,
        end: components.length
      };
    }
    var startIndex = Math.floor(scrollTop / itemHeight);
    var endIndex = Math.min(startIndex + Math.ceil(containerHeight / itemHeight) + overscan, components.length);
    return {
      start: Math.max(0, startIndex - overscan),
      end: endIndex
    };
  }, [scrollTop, itemHeight, containerHeight, overscan, components.length, enableVirtualization, containerRef]);

  // Update visible range when scroll changes
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var newRange = calculateVisibleRange();
    setVisibleRange(newRange);
  }, [calculateVisibleRange]);

  // Throttled scroll handler
  var handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((0,lodash__WEBPACK_IMPORTED_MODULE_3__.throttle)(function (event) {
    if (event.target) {
      setScrollTop(event.target.scrollTop);
    }
  }, 16),
  // ~60fps
  []);

  // Get visible components for rendering
  var visibleComponents = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    if (!enableVirtualization) {
      return components.map(function (component, index) {
        return {
          component: component,
          index: index
        };
      });
    }
    return components.slice(visibleRange.start, visibleRange.end).map(function (component, relativeIndex) {
      return {
        component: component,
        index: visibleRange.start + relativeIndex
      };
    });
  }, [components, visibleRange, enableVirtualization]);

  // Component caching for performance
  var getCachedComponent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (componentId, renderFunction) {
    var cacheKey = "".concat(componentId, "_").concat(JSON.stringify(components.find(function (c) {
      return c.id === componentId;
    })));
    if (componentCache.current.has(cacheKey)) {
      return componentCache.current.get(cacheKey);
    }
    var renderedComponent = renderFunction();
    componentCache.current.set(cacheKey, renderedComponent);

    // Limit cache size to prevent memory leaks
    if (componentCache.current.size > 100) {
      var firstKey = componentCache.current.keys().next().value;
      componentCache.current["delete"](firstKey);
    }
    return renderedComponent;
  }, [components]);

  // Performance monitoring
  var startRenderMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (enablePerformanceMonitoring) {
      renderStartTime.current = performance.now();
    }
  }, [enablePerformanceMonitoring]);
  var endRenderMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (enablePerformanceMonitoring && renderStartTime.current > 0) {
      var renderDuration = performance.now() - renderStartTime.current;
      setRenderTime(renderDuration);
      renderStartTime.current = 0;
    }
  }, [enablePerformanceMonitoring]);

  // Frame rate monitoring
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enablePerformanceMonitoring) return;
    var animationId;
    var _measureFrameRate = function measureFrameRate() {
      var now = performance.now();
      var delta = now - lastFrameTime.current;
      if (delta >= 1000) {
        var fps = Math.round(frameCount.current * 1000 / delta);
        setFrameRate(fps);
        frameCount.current = 0;
        lastFrameTime.current = now;
      } else {
        frameCount.current++;
      }
      animationId = requestAnimationFrame(_measureFrameRate);
    };
    animationId = requestAnimationFrame(_measureFrameRate);
    return function () {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [enablePerformanceMonitoring]);

  // Memory usage monitoring
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enablePerformanceMonitoring || !performance.memory) return;
    var measureMemory = function measureMemory() {
      var memoryInfo = performance.memory;
      var usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
      setMemoryUsage(usedMB);
    };
    var interval = setInterval(measureMemory, 5000);
    measureMemory(); // Initial measurement

    return function () {
      return clearInterval(interval);
    };
  }, [enablePerformanceMonitoring]);

  // Intersection Observer for lazy loading
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enableVirtualization) return;
    intersectionObserver.current = new IntersectionObserver(function (entries) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          // Component is visible, ensure it's rendered
          var componentId = entry.target.dataset.componentId;
          if (componentId) {
            // Trigger re-render if needed
          }
        }
      });
    }, {
      root: containerRef,
      rootMargin: "".concat(overscan * itemHeight, "px"),
      threshold: 0.1
    });
    return function () {
      if (intersectionObserver.current) {
        intersectionObserver.current.disconnect();
      }
    };
  }, [containerRef, overscan, itemHeight, enableVirtualization]);

  // Clear cache when components change significantly
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var componentIds = new Set(components.map(function (c) {
      return c.id;
    }));
    var cachedIds = new Set(Array.from(componentCache.current.keys()).map(function (key) {
      return key.split('_')[0];
    }));

    // Remove cached components that no longer exist
    cachedIds.forEach(function (cachedId) {
      if (!componentIds.has(cachedId)) {
        Array.from(componentCache.current.keys()).filter(function (key) {
          return key.startsWith(cachedId);
        }).forEach(function (key) {
          return componentCache.current["delete"](key);
        });
      }
    });
  }, [components]);

  // Get container props for virtualization
  var getContainerProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization) {
      return {};
    }
    return {
      ref: setContainerRef,
      onScroll: handleScroll,
      style: {
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }
    };
  }, [enableVirtualization, containerHeight, handleScroll]);

  // Get spacer props for virtual scrolling
  var getSpacerProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization) {
      return {
        before: {},
        after: {}
      };
    }
    var totalHeight = components.length * itemHeight;
    var beforeHeight = visibleRange.start * itemHeight;
    var afterHeight = totalHeight - visibleRange.end * itemHeight;
    return {
      before: {
        style: {
          height: beforeHeight,
          width: '100%'
        }
      },
      after: {
        style: {
          height: afterHeight,
          width: '100%'
        }
      }
    };
  }, [enableVirtualization, components.length, itemHeight, visibleRange]);

  // Performance optimization utilities
  var optimizationUtils = {
    clearCache: function clearCache() {
      return componentCache.current.clear();
    },
    getCacheSize: function getCacheSize() {
      return componentCache.current.size;
    },
    getPerformanceMetrics: function getPerformanceMetrics() {
      return {
        renderTime: renderTime,
        frameRate: frameRate,
        memoryUsage: memoryUsage,
        cacheSize: componentCache.current.size,
        visibleComponents: visibleComponents.length,
        totalComponents: components.length
      };
    },
    shouldRender: function shouldRender(componentId) {
      // Check if component should be rendered based on visibility
      if (!enableVirtualization) return true;
      var componentIndex = components.findIndex(function (c) {
        return c.id === componentId;
      });
      return componentIndex >= visibleRange.start && componentIndex < visibleRange.end;
    }
  };
  return _objectSpread({
    // Virtualization
    visibleComponents: visibleComponents,
    visibleRange: visibleRange,
    getContainerProps: getContainerProps,
    getSpacerProps: getSpacerProps,
    // Performance monitoring
    renderTime: renderTime,
    frameRate: frameRate,
    memoryUsage: memoryUsage,
    startRenderMeasurement: startRenderMeasurement,
    endRenderMeasurement: endRenderMeasurement,
    // Caching
    getCachedComponent: getCachedComponent
  }, optimizationUtils);
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (usePreviewPerformance);

/***/ }),

/***/ 94588:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Cd: () => (/* binding */ useSelection),
/* harmony export */   EF: () => (/* binding */ useContextMenu),
/* harmony export */   KW: () => (/* binding */ useKeyboardShortcuts),
/* harmony export */   R2: () => (/* binding */ useLoadingState),
/* harmony export */   aD: () => (/* binding */ useUndoRedo),
/* harmony export */   iD: () => (/* binding */ useClipboard)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/**
 * Hook for managing undo/redo functionality
 * @param {*} initialState - Initial state value
 * @param {number} maxHistorySize - Maximum number of history entries to keep
 * @returns {Object} State and undo/redo functions
 */
var useUndoRedo = function useUndoRedo(initialState) {
  var maxHistorySize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 50;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([initialState]),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    history = _useState2[0],
    setHistory = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    currentIndex = _useState4[0],
    setCurrentIndex = _useState4[1];
  var isUndoRedoAction = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);

  // Get current state
  var currentState = history[currentIndex];

  // Push new state to history
  var pushState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (newState) {
    if (isUndoRedoAction.current) {
      isUndoRedoAction.current = false;
      return;
    }
    setHistory(function (prev) {
      var newHistory = prev.slice(0, currentIndex + 1);
      newHistory.push(newState);

      // Limit history size
      if (newHistory.length > maxHistorySize) {
        newHistory.shift();
        return newHistory;
      }
      return newHistory;
    });
    setCurrentIndex(function (prev) {
      var newIndex = Math.min(prev + 1, maxHistorySize - 1);
      return newIndex;
    });
  }, [currentIndex, maxHistorySize]);

  // Undo function
  var undo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (currentIndex > 0) {
      isUndoRedoAction.current = true;
      setCurrentIndex(function (prev) {
        return prev - 1;
      });
      return history[currentIndex - 1];
    }
    return currentState;
  }, [currentIndex, history, currentState]);

  // Redo function
  var redo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (currentIndex < history.length - 1) {
      isUndoRedoAction.current = true;
      setCurrentIndex(function (prev) {
        return prev + 1;
      });
      return history[currentIndex + 1];
    }
    return currentState;
  }, [currentIndex, history, currentState]);

  // Check if undo is available
  var canUndo = currentIndex > 0;

  // Check if redo is available
  var canRedo = currentIndex < history.length - 1;

  // Clear history
  var clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setHistory([currentState]);
    setCurrentIndex(0);
  }, [currentState]);

  // Get history info
  var getHistoryInfo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return {
      totalStates: history.length,
      currentIndex: currentIndex,
      canUndo: canUndo,
      canRedo: canRedo
    };
  }, [history.length, currentIndex, canUndo, canRedo]);
  return {
    state: currentState,
    pushState: pushState,
    undo: undo,
    redo: redo,
    canUndo: canUndo,
    canRedo: canRedo,
    clearHistory: clearHistory,
    getHistoryInfo: getHistoryInfo
  };
};

/**
 * Hook for keyboard shortcuts
 * @param {Object} shortcuts - Object mapping key combinations to functions
 * @param {Array} dependencies - Dependencies for the effect
 */
var useKeyboardShortcuts = function useKeyboardShortcuts(shortcuts) {
  var dependencies = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var handleKeyDown = function handleKeyDown(event) {
      var ctrlKey = event.ctrlKey,
        metaKey = event.metaKey,
        shiftKey = event.shiftKey,
        altKey = event.altKey,
        key = event.key;

      // Create key combination string
      var modifiers = [];
      if (ctrlKey || metaKey) modifiers.push('ctrl');
      if (shiftKey) modifiers.push('shift');
      if (altKey) modifiers.push('alt');
      var combination = [].concat(modifiers, [key.toLowerCase()]).join('+');

      // Check if combination exists in shortcuts
      if (shortcuts[combination]) {
        event.preventDefault();
        shortcuts[combination](event);
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return function () {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, dependencies);
};

/**
 * Hook for managing contextual menus
 * @returns {Object} Context menu state and functions
 */
var useContextMenu = function useContextMenu() {
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      visible: false,
      x: 0,
      y: 0,
      items: []
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    contextMenu = _useState6[0],
    setContextMenu = _useState6[1];
  var showContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (event, items) {
    event.preventDefault();
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      items: items || []
    });
  }, []);
  var hideContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setContextMenu(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        visible: false
      });
    });
  }, []);

  // Hide context menu when clicking outside
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var handleClick = function handleClick() {
      if (contextMenu.visible) {
        hideContextMenu();
      }
    };
    document.addEventListener('click', handleClick);
    return function () {
      document.removeEventListener('click', handleClick);
    };
  }, [contextMenu.visible, hideContextMenu]);
  return {
    contextMenu: contextMenu,
    showContextMenu: showContextMenu,
    hideContextMenu: hideContextMenu
  };
};

/**
 * Hook for managing loading states with debouncing
 * @param {number} delay - Delay before showing loading state
 * @returns {Object} Loading state and functions
 */
var useLoadingState = function useLoadingState() {
  var delay = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 200;
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    isLoading = _useState8[0],
    setIsLoading = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(''),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    loadingMessage = _useState0[0],
    setLoadingMessage = _useState0[1];
  var timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
  var startLoading = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    var message = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Loading...';
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(function () {
      setIsLoading(true);
      setLoadingMessage(message);
    }, delay);
  }, [delay]);
  var stopLoading = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsLoading(false);
    setLoadingMessage('');
  }, []);

  // Cleanup timeout on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    return function () {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  return {
    isLoading: isLoading,
    loadingMessage: loadingMessage,
    startLoading: startLoading,
    stopLoading: stopLoading
  };
};

/**
 * Hook for managing component selection with multi-select support
 * @param {Array} items - Array of selectable items
 * @returns {Object} Selection state and functions
 */
var useSelection = function useSelection() {
  var items = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set()),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    selectedItems = _useState10[0],
    setSelectedItems = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(-1),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState11, 2),
    lastSelectedIndex = _useState12[0],
    setLastSelectedIndex = _useState12[1];
  var selectItem = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (item) {
    var multiSelect = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var itemIndex = items.findIndex(function (i) {
      return i.id === item.id;
    });
    setSelectedItems(function (prev) {
      var newSelection = new Set(multiSelect ? prev : []);
      if (newSelection.has(item.id)) {
        newSelection["delete"](item.id);
      } else {
        newSelection.add(item.id);
      }
      return newSelection;
    });
    setLastSelectedIndex(itemIndex);
  }, [items]);
  var selectRange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (item) {
    var itemIndex = items.findIndex(function (i) {
      return i.id === item.id;
    });
    if (lastSelectedIndex !== -1) {
      var start = Math.min(lastSelectedIndex, itemIndex);
      var end = Math.max(lastSelectedIndex, itemIndex);
      setSelectedItems(function (prev) {
        var newSelection = new Set(prev);
        for (var i = start; i <= end; i++) {
          if (items[i]) {
            newSelection.add(items[i].id);
          }
        }
        return newSelection;
      });
    } else {
      selectItem(item);
    }
  }, [items, lastSelectedIndex, selectItem]);
  var selectAll = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setSelectedItems(new Set(items.map(function (item) {
      return item.id;
    })));
  }, [items]);
  var clearSelection = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setSelectedItems(new Set());
    setLastSelectedIndex(-1);
  }, []);
  var isSelected = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (itemId) {
    return selectedItems.has(itemId);
  }, [selectedItems]);
  var getSelectedItems = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return items.filter(function (item) {
      return selectedItems.has(item.id);
    });
  }, [items, selectedItems]);
  return {
    selectedItems: Array.from(selectedItems),
    selectItem: selectItem,
    selectRange: selectRange,
    selectAll: selectAll,
    clearSelection: clearSelection,
    isSelected: isSelected,
    getSelectedItems: getSelectedItems,
    selectedCount: selectedItems.size
  };
};

/**
 * Hook for managing clipboard operations
 * @returns {Object} Clipboard functions
 */
var useClipboard = function useClipboard() {
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState13, 2),
    clipboardData = _useState14[0],
    setClipboardData = _useState14[1];
  var copy = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (data) {
    setClipboardData(data);

    // Also copy to system clipboard if possible
    if (navigator.clipboard && typeof data === 'string') {
      navigator.clipboard.writeText(data)["catch"](console.error);
    }
  }, []);
  var paste = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return clipboardData;
  }, [clipboardData]);
  var clear = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setClipboardData(null);
  }, []);
  var hasData = clipboardData !== null;
  return {
    copy: copy,
    paste: paste,
    clear: clear,
    hasData: hasData,
    data: clipboardData
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (useUndoRedo)));

/***/ })

}]);